'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../components/auth-provider';
import { collection, query, where, orderBy, getDocs, limit } from 'firebase/firestore';
import { db } from '../../../lib/firebase';
import { JournalEntry } from '@metiscore/types';

// Mock data for demonstration - replace with real API integration
const mockSentimentData = {
  overall_trend: 'improving',
  average_sentiment: 0.65,
  emotional_patterns: [
    { date: '2025-01-01', sentiment: 0.4, primary_emotion: 'anxious' },
    { date: '2025-01-02', sentiment: 0.6, primary_emotion: 'hopeful' },
    { date: '2025-01-03', sentiment: 0.7, primary_emotion: 'content' },
    { date: '2025-01-04', sentiment: 0.5, primary_emotion: 'frustrated' },
    { date: '2025-01-05', sentiment: 0.8, primary_emotion: 'optimistic' },
  ],
  symptom_correlations: [
    { symptom: 'hot_flashes', sentiment_impact: -0.3, frequency: 15 },
    { symptom: 'sleep_issues', sentiment_impact: -0.4, frequency: 12 },
    { symptom: 'mood_swings', sentiment_impact: -0.5, frequency: 8 },
  ],
  insights: [
    'Your emotional wellness has improved by 25% over the past month',
    'Sleep quality appears to significantly impact your daily mood',
    'Hot flashes are most frequent in the evening, affecting next-day sentiment',
  ]
};

export default function AnalyticsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [isPremium, setIsPremium] = useState(false); // TODO: Implement subscription check

  useEffect(() => {
    if (!user) {
      router.push('/');
      return;
    }

    loadJournalData();
  }, [user, router]);

  const loadJournalData = async () => {
    if (!user) return;

    try {
      const q = query(
        collection(db, 'journal_entries'),
        where('userId', '==', user.uid),
        orderBy('createdAt', 'desc'),
        limit(30) // Last 30 entries for analysis
      );

      const snapshot = await getDocs(q);
      const entries = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
      })) as JournalEntry[];

      setJournalEntries(entries);
    } catch (error) {
      console.error('Error loading journal data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generatePDFReport = async () => {
    // TODO: Implement PDF generation
    alert('PDF generation will be implemented - this will create a comprehensive report for healthcare providers');
  };

  if (!user) return null;

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Loading your analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">📊</div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Wellness Analytics</h1>
                <p className="text-sm text-gray-600">AI-powered insights into your menopause journey</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={generatePDFReport}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <span>📄</span>
                <span>Share With Doctor</span>
              </button>
              <button
                type="button"
                onClick={() => router.push('/dashboard')}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                ← Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {!isPremium && (
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold mb-2">🌟 Unlock Premium Analytics</h2>
                <p className="text-purple-100">Get detailed insights, trend analysis, and professional reports</p>
              </div>
              <button
                type="button"
                className="px-6 py-3 bg-white text-purple-600 rounded-lg font-semibold hover:bg-purple-50 transition-colors"
              >
                Upgrade Now
              </button>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: '📈' },
                { id: 'sentiment', label: 'Sentiment Trends', icon: '😊' },
                { id: 'symptoms', label: 'Symptom Patterns', icon: '🌡️' },
                { id: 'insights', label: 'AI Insights', icon: '🧠' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content Area */}
        <div className="space-y-6">
          {activeTab === 'overview' && (
            <div className="grid md:grid-cols-3 gap-6">
              {/* Summary Cards */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Journal Entries</p>
                    <p className="text-2xl font-bold text-gray-900">{journalEntries.length}</p>
                  </div>
                  <div className="text-3xl">📝</div>
                </div>
                <p className="text-xs text-gray-500 mt-2">Last 30 days</p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Sentiment</p>
                    <p className="text-2xl font-bold text-gray-900">{(mockSentimentData.average_sentiment * 100).toFixed(0)}%</p>
                  </div>
                  <div className="text-3xl">😊</div>
                </div>
                <p className="text-xs text-green-600 mt-2">↗ Improving trend</p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Wellness Score</p>
                    <p className="text-2xl font-bold text-gray-900">7.2/10</p>
                  </div>
                  <div className="text-3xl">⭐</div>
                </div>
                <p className="text-xs text-blue-600 mt-2">Based on AI analysis</p>
              </div>
            </div>
          )}

          {activeTab === 'sentiment' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Sentiment Trends Over Time</h3>
              {isPremium ? (
                <div className="space-y-4">
                  <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <p className="text-gray-500">📈 Interactive sentiment chart would go here</p>
                  </div>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-900 mb-2">Most Common Emotions</h4>
                      <ul className="space-y-1 text-sm text-blue-800">
                        <li>😊 Hopeful (35%)</li>
                        <li>😌 Content (28%)</li>
                        <li>😟 Anxious (22%)</li>
                        <li>😤 Frustrated (15%)</li>
                      </ul>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-900 mb-2">Positive Trends</h4>
                      <ul className="space-y-1 text-sm text-green-800">
                        <li>✓ 25% improvement in overall mood</li>
                        <li>✓ Reduced anxiety episodes</li>
                        <li>✓ Better emotional regulation</li>
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🔒</div>
                    <p className="text-gray-600 mb-4">Premium feature - Upgrade to see detailed sentiment analysis</p>
                    <button
                      type="button"
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                    >
                      Upgrade Now
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'symptoms' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Symptom Pattern Analysis</h3>
              <div className="space-y-4">
                {mockSentimentData.symptom_correlations.map((symptom, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900 capitalize">
                        {symptom.symptom.replace('_', ' ')}
                      </h4>
                      <p className="text-sm text-gray-600">Frequency: {symptom.frequency} times this month</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        Impact: {symptom.sentiment_impact > 0 ? '+' : ''}{(symptom.sentiment_impact * 100).toFixed(0)}%
                      </p>
                      <div className={`w-16 h-2 rounded-full ${
                        symptom.sentiment_impact < -0.3 ? 'bg-red-400' : 
                        symptom.sentiment_impact < 0 ? 'bg-yellow-400' : 'bg-green-400'
                      }`}></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'insights' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">AI-Generated Insights</h3>
              <div className="space-y-4">
                {mockSentimentData.insights.map((insight, index) => (
                  <div key={index} className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl">💡</div>
                    <div>
                      <p className="text-blue-900">{insight}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
