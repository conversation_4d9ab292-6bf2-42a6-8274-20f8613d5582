// Sentiment Analysis Service for MenoWellness
import { SentimentAnalysisResponse, RichAnalysisResponse } from '@metiscore/types';

export interface SentimentAnalysisRequest {
  text: string;
  userId: string;
  context?: 'menopause' | 'general';
  includePartnerInsights?: boolean;
}

export class SentimentAnalysisService {
  private static readonly API_URL = process.env.NEXT_PUBLIC_SENTIMENT_API_URL || 'https://api.openai.com/v1/chat/completions';
  private static readonly TIMEOUT_MS = 30000; // 30 seconds

  /**
   * Analyze text sentiment with menopause-specific context
   */
  static async analyzeSentiment(request: SentimentAnalysisRequest): Promise<SentimentAnalysisResponse | RichAnalysisResponse> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT_MS);

      // For now, we'll use a mock implementation since the original API isn't available
      // In production, replace this with actual API integration
      const response = await this.mockSentimentAnalysis(request);
      
      clearTimeout(timeoutId);
      return response;

    } catch (error) {
      console.error('Sentiment analysis failed:', error);
      return this.getFallbackResponse();
    }
  }

  /**
   * Mock sentiment analysis for development/testing
   * Replace with actual API integration in production
   */
  private static async mockSentimentAnalysis(request: SentimentAnalysisRequest): Promise<SentimentAnalysisResponse | RichAnalysisResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const text = request.text.toLowerCase();
    
    // Basic sentiment scoring based on keywords
    const positiveWords = ['good', 'great', 'happy', 'better', 'improving', 'hopeful', 'positive', 'grateful', 'calm', 'peaceful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'worse', 'frustrated', 'angry', 'sad', 'overwhelmed', 'anxious', 'depressed'];
    const menopauseSymptoms = ['hot flash', 'night sweat', 'mood swing', 'insomnia', 'fatigue', 'brain fog', 'irritable'];

    let sentimentScore = 0.5; // Neutral baseline
    let emotionalIntensity = 0.5;
    let primaryEmotion = 'neutral';
    let riskLevel = 'low';

    // Analyze sentiment
    positiveWords.forEach(word => {
      if (text.includes(word)) sentimentScore += 0.1;
    });
    
    negativeWords.forEach(word => {
      if (text.includes(word)) sentimentScore -= 0.1;
    });

    // Analyze menopause-specific content
    const symptomCount = menopauseSymptoms.filter(symptom => text.includes(symptom)).length;
    if (symptomCount > 0) {
      sentimentScore -= symptomCount * 0.05;
      emotionalIntensity += symptomCount * 0.1;
    }

    // Determine primary emotion
    if (sentimentScore > 0.7) primaryEmotion = 'optimistic';
    else if (sentimentScore > 0.6) primaryEmotion = 'hopeful';
    else if (sentimentScore > 0.4) primaryEmotion = 'content';
    else if (sentimentScore > 0.3) primaryEmotion = 'frustrated';
    else if (sentimentScore > 0.2) primaryEmotion = 'anxious';
    else primaryEmotion = 'distressed';

    // Assess risk level
    if (text.includes('suicidal') || text.includes('harm myself') || text.includes('end it all')) {
      riskLevel = 'high';
    } else if (sentimentScore < 0.2 || emotionalIntensity > 0.8) {
      riskLevel = 'medium';
    }

    // Clamp values
    sentimentScore = Math.max(0, Math.min(1, sentimentScore));
    emotionalIntensity = Math.max(0, Math.min(1, emotionalIntensity));

    const baseResponse: SentimentAnalysisResponse = {
      insights: {
        overall_assessment: this.generateOverallAssessment(sentimentScore, primaryEmotion, symptomCount)
      },
      sentiment: {
        category: sentimentScore > 0.6 ? 'positive' : sentimentScore > 0.4 ? 'neutral' : 'negative',
        score: sentimentScore
      },
      emotions: {
        primary: primaryEmotion,
        emotional_intensity: emotionalIntensity
      },
      crisisAssessment: {
        risk_level: riskLevel
      }
    };

    // Add partner insights if requested
    if (request.includePartnerInsights) {
      const richResponse: RichAnalysisResponse = {
        ...baseResponse,
        partnerSupportInsights: {
          relationshipHealth: this.assessRelationshipHealth(sentimentScore, text),
          supportRecommendations: this.generateSupportRecommendations(primaryEmotion, symptomCount),
          relationshipImpact: this.assessRelationshipImpact(sentimentScore, emotionalIntensity),
          actionableSteps: this.generateActionableSteps(primaryEmotion, riskLevel)
        },
        relationshipSupport: this.generateSupportRecommendations(primaryEmotion, symptomCount),
        riskAssessment: {
          concernLevel: riskLevel as 'low' | 'medium' | 'high',
          recommendedActions: this.getRecommendedActions(riskLevel, primaryEmotion),
          riskLevel: riskLevel
        }
      };
      return richResponse;
    }

    return baseResponse;
  }

  private static generateOverallAssessment(sentimentScore: number, emotion: string, symptomCount: number): string {
    if (sentimentScore > 0.7) {
      return "You're showing strong emotional resilience and positive coping strategies. Your menopause journey appears to be well-managed.";
    } else if (sentimentScore > 0.5) {
      return "You're maintaining a balanced emotional state despite menopause challenges. Consider focusing on self-care strategies.";
    } else if (sentimentScore > 0.3) {
      return "You're experiencing some emotional challenges related to menopause symptoms. Support and coping strategies may be beneficial.";
    } else {
      return "You're going through a particularly challenging time. Consider reaching out to healthcare providers or support networks.";
    }
  }

  private static assessRelationshipHealth(sentimentScore: number, text: string): string {
    const relationshipWords = ['partner', 'husband', 'spouse', 'relationship', 'support', 'understanding'];
    const hasRelationshipContent = relationshipWords.some(word => text.includes(word));
    
    if (!hasRelationshipContent) return 'No relationship indicators detected';
    
    if (sentimentScore > 0.6) return 'Strong relationship support evident';
    else if (sentimentScore > 0.4) return 'Moderate relationship dynamics';
    else return 'Relationship stress indicators present';
  }

  private static generateSupportRecommendations(emotion: string, symptomCount: number): string[] {
    const recommendations = [];
    
    switch (emotion) {
      case 'anxious':
        recommendations.push('Practice active listening without trying to fix everything');
        recommendations.push('Offer reassurance and validate their feelings');
        recommendations.push('Suggest relaxation activities you can do together');
        break;
      case 'frustrated':
        recommendations.push('Give them space when needed, but stay available');
        recommendations.push('Help with practical tasks to reduce stress');
        recommendations.push('Avoid taking mood changes personally');
        break;
      case 'distressed':
        recommendations.push('Encourage professional support if needed');
        recommendations.push('Be extra patient and understanding');
        recommendations.push('Focus on creating a calm, supportive environment');
        break;
      default:
        recommendations.push('Continue providing consistent emotional support');
        recommendations.push('Celebrate small wins and positive moments');
    }

    if (symptomCount > 2) {
      recommendations.push('Learn about menopause symptoms to better understand their experience');
      recommendations.push('Help track symptoms and patterns together');
    }

    return recommendations;
  }

  private static assessRelationshipImpact(sentimentScore: number, intensity: number): string {
    if (intensity > 0.7) {
      return 'High emotional intensity may be affecting relationship dynamics';
    } else if (sentimentScore < 0.3) {
      return 'Current emotional state may create relationship challenges';
    } else {
      return 'Emotional state appears manageable within relationship context';
    }
  }

  private static generateActionableSteps(emotion: string, riskLevel: string): string[] {
    const steps = [];
    
    if (riskLevel === 'high') {
      steps.push('Encourage immediate professional help');
      steps.push('Stay close and monitor their wellbeing');
      steps.push('Remove any potential stressors');
    } else if (riskLevel === 'medium') {
      steps.push('Suggest scheduling a healthcare appointment');
      steps.push('Increase daily check-ins and support');
      steps.push('Help identify and address specific triggers');
    } else {
      steps.push('Maintain regular communication about their needs');
      steps.push('Plan enjoyable activities together');
      steps.push('Continue learning about menopause together');
    }

    return steps;
  }

  private static getRecommendedActions(riskLevel: string, emotion: string): string[] {
    const actions = [];
    
    switch (riskLevel) {
      case 'high':
        actions.push('Seek immediate professional help');
        actions.push('Contact healthcare provider urgently');
        actions.push('Ensure safety and remove stressors');
        break;
      case 'medium':
        actions.push('Schedule healthcare appointment within 1-2 weeks');
        actions.push('Increase support network engagement');
        actions.push('Monitor symptoms and mood patterns');
        break;
      default:
        actions.push('Continue current support strategies');
        actions.push('Maintain regular wellness check-ins');
        actions.push('Focus on self-care and stress management');
    }

    return actions;
  }

  private static getFallbackResponse(): SentimentAnalysisResponse {
    return {
      insights: {
        overall_assessment: 'Analysis temporarily unavailable. Please try again later.'
      },
      sentiment: {
        category: 'neutral',
        score: 0.5
      },
      emotions: {
        primary: 'neutral',
        emotional_intensity: 0.5
      },
      crisisAssessment: {
        risk_level: 'low'
      }
    };
  }

  /**
   * Batch analyze multiple journal entries for trend analysis
   */
  static async batchAnalyze(entries: Array<{ text: string; date: Date }>): Promise<Array<SentimentAnalysisResponse & { date: Date }>> {
    const results = [];
    
    for (const entry of entries) {
      try {
        const analysis = await this.analyzeSentiment({
          text: entry.text,
          userId: 'batch-analysis',
          context: 'menopause'
        }) as SentimentAnalysisResponse;
        
        results.push({
          ...analysis,
          date: entry.date
        });
      } catch (error) {
        console.error('Batch analysis error for entry:', error);
        results.push({
          ...this.getFallbackResponse(),
          date: entry.date
        });
      }
    }
    
    return results;
  }
}
